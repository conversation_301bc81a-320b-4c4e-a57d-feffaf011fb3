group 'me.wolszon.app_group_directory'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()  // Updated from jcenter
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'  // Updated from 3.2.1
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()  // Updated from jcenter
    }
}

apply plugin: 'com.android.library'

android {
    compileSdk 35  // Updated from 28 to support lStar attribute
    namespace 'me.wolszon.app_group_directory'
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdk 24  // Updated from 16
        targetSdk 34  // Added for compatibility
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"  // Updated
    }

    lintOptions {
        disable 'InvalidPackage'
    }
}