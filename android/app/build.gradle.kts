plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

// Add these imports at the top
import java.util.Properties
import java.io.FileInputStream

val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "io.boet.sluqe"
    compileSdk = 35  // Required by androidx dependencies
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "io.boet.sluqe"
        minSdk = 24
        targetSdk = 34  // Keep targetSdk at 34 for stability
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        create("release") {
            // Try to get from key.properties first (for local builds)
            val alias = keystoreProperties["keyAlias"] as String?
            val keyPass = keystoreProperties["keyPassword"] as String?
            val storePath = keystoreProperties["storeFile"] as String?
            val storePass = keystoreProperties["storePassword"] as String?

            // If key.properties exists and has all values
            if (!alias.isNullOrBlank() && !keyPass.isNullOrBlank() && 
                !storePath.isNullOrBlank() && !storePass.isNullOrBlank()) {
                keyAlias = alias
                keyPassword = keyPass
                storeFile = file(storePath)
                storePassword = storePass
            } 
            // Otherwise try to get from environment variables (Codemagic)
            else if (System.getenv("CM_KEYSTORE_PATH") != null) {
                keyAlias = System.getenv("CM_KEY_ALIAS")
                keyPassword = System.getenv("CM_KEY_PASSWORD")
                storeFile = file(System.getenv("CM_KEYSTORE_PATH"))
                storePassword = System.getenv("CM_KEYSTORE_PASSWORD")
            }
            // Fallback: try the environment variables you have in Codemagic
            else if (System.getenv("KEY_ALIAS") != null) {
                keyAlias = System.getenv("KEY_ALIAS")
                keyPassword = System.getenv("KEY_PASSWORD")
                storeFile = System.getenv("KEYSTORE_PATH")?.let { file(it) }
                storePassword = System.getenv("KEYSTORE_PASSWORD")
            }
        }
    }

    buildTypes {
        getByName("release") {
            // Only apply signing if we have a signing config
            try {
                signingConfig = signingConfigs.getByName("release")
            } catch (e: Exception) {
                println("Warning: Release signing config not found, building unsigned APK")
            }
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    // Add packaging options to handle resource conflicts
    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            excludes += "/META-INF/versions/9/previous-compilation-data.bin"
        }
    }
}

// Correct way to handle dependency conflicts in Kotlin DSL
configurations.all {
    resolutionStrategy {
        // Force specific versions to avoid conflicts
        force("org.jetbrains.kotlin:kotlin-stdlib:2.1.0")
        force("org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0")
        force("org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0")
        force("org.jetbrains.kotlin:kotlin-stdlib-common:2.1.0")

        // Force androidx.core to compatible version
        force("androidx.core:core:1.13.1")
        force("androidx.core:core-ktx:1.13.1")

        // Prevent incompatible versions
        eachDependency {
            if (requested.group == "androidx.core") {
                if (requested.name == "core") {
                    useVersion("1.13.1")
                    because("Ensuring compatible androidx.core version")
                }
                if (requested.name == "core-ktx") {
                    useVersion("1.13.1")
                    because("Ensuring compatible androidx.core version")
                }
            }
        }
    }
}

// Add explicit dependencies to ensure compatibility
dependencies {
    implementation("androidx.core:core:1.13.1")
    implementation("androidx.core:core-ktx:1.13.1")
}

flutter {
    source = "../.."
}