class Audio {
  final int duration;
  final List<String> embeddingIds;
  final String folderId;
  String? folderName;
  final bool isDirty;
  final String processingState;
  final String richText;
  final List<String> speakers;
  final String storagePath;
  final String title;
  final List<Transcription> transcript;
  final List<ActionItem> actionItems;
  final DateTime createdAt;
  DateTime? deletedAt;

  Audio({
    required this.createdAt,
    required this.actionItems,
    required this.duration,
    required this.embeddingIds,
    required this.folderId,
    this.folderName,
    required this.isDirty,
    required this.processingState,
    required this.richText,
    required this.speakers,
    required this.storagePath,
    required this.title,
    required this.transcript,
  });
  factory Audio.fromMap(Map<String, dynamic> map) {
    return Audio(
      createdAt: map['createdAt'].toDate(),
      actionItems:
          (map['actionItems'] as List)
              .map((e) => ActionItem.fromMap(e))
              .toList(),
      duration: map['duration'],
      embeddingIds: List<String>.from(map['embeddingIds'] ?? []),
      folderId: map['folderId'],
      folderName: map['folderName'],
      isDirty: map['isDirty'],
      processingState: map['processingState'],
      richText: map['richText'],
      speakers:
          (map['speakers'] as List<dynamic>? ?? [])
              .map(
                (speaker) =>
                    speaker.toString().replaceAll(RegExp(r'^\[|\]$'), ''),
              )
              .toList(),
      storagePath: map['storagePath'],
      title: map['title'],
      transcript:
          (map['transcript'] as List<dynamic>)
              .map((e) => Transcription.fromMap(e))
              .toList(),
    );
  }
}

// Chat will be available in mobile app very soon. Meanwhile please use sluqe webapp for chat.
// <EMAIL>

class Transcription {
  final String text;
  final double timestamp;
  Transcription({required this.text, required this.timestamp});

  factory Transcription.fromMap(Map<String, dynamic> map) =>
      Transcription(
        text: (map['text'] as String?)?.trim().replaceAll(RegExp(r'\s+'), ' ') ?? '',
        timestamp: map['timestamp'].toDouble()
      );
}

class ActionItem {
  final String assignedTo;
  final String dueDate;
  final String text;
  final double timestamp;
  ActionItem({
    required this.assignedTo,
    required this.dueDate,
    required this.text,
    required this.timestamp,
  });

  //from map
  factory ActionItem.fromMap(Map<String, dynamic> map) => ActionItem(
    assignedTo: map['assignedTo'],
    dueDate: map['dueDate'],
    text: map['text'],
    timestamp:map['timestamp'].runtimeType == int ? map['timestamp'].toDouble() : map['timestamp'],
  );
}
