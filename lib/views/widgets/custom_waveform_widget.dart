import 'package:flutter/material.dart';
import 'package:waveform_flutter/waveform_flutter.dart' as waveform;

class CustomWaveformWidget extends StatelessWidget {
  final Stream<waveform.Amplitude> amplitudeStream;
  final double height;
  final Color waveColor;
  final double waveWidth;
  final int maxBars;

  const CustomWaveformWidget({
    Key? key,
    required this.amplitudeStream,
    this.height = 3,
    this.waveColor = Colors.blue,
    this.waveWidth = 3.0,
    this.maxBars = 50,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      child: waveform.AnimatedWaveList(
        stream: amplitudeStream,
        barBuilder:
            (animation, amplitude) => waveform.WaveFormBar(
              animation: animation,
              amplitude: amplitude,
              color: waveColor,
              maxHeight: height.toInt(),
            ),
      ),
    );
  }
}
