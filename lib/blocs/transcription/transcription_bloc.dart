import 'dart:async';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/services/audio/audio_player_services.dart';

part 'transcription_event.dart';
part 'transcription_state.dart';

class TranscriptionBloc extends Bloc<TranscriptionEvent, TranscriptionState> {
  late final PlayerController _playerController;
  final AudioPlayerService _audioService;
  StreamSubscription? _playerStateSubscription;
  StreamSubscription? _playerPositionSubscription;
  Timer? _positionTimer;

  TranscriptionBloc({AudioPlayerService? audioService})
    : _audioService = audioService ?? AudioPlayerService(),
      super(const TranscriptionState()) {
    _playerController = PlayerController();
    _initializePlayerListeners();
    on<InitializeAudioPlayer>(_onInitializeAudioPlayer);
    on<PlayAudio>(_onPlayAudio);
    on<PauseAudio>(_onPauseAudio);
    on<StopAudio>(_onStopAudio);
    on<SeekAudio>(_onSeekAudio);
    on<UpdatePlayerPosition>(_onUpdatePlayerPosition);
    on<UpdatePlayerState>(_onUpdatePlayerState);
    on<SearchTranscript>(_onSearchTranscript);
    on<JumpToTranscript>(_onJumpToTranscript);
    on<PlayerError>(_onPlayerError);
    on<WaveformReady>(_onWaveformReady);
    on<RecalculateWaveform>(_onRecalculateWaveform);
    on<DisposePlayer>(_onDisposePlayer);
  }

  void _initializePlayerListeners() {
    _playerStateSubscription = _playerController.onPlayerStateChanged.listen((
      playerState,
    ) {
      add(UpdatePlayerState(playerState));

      // Start/stop position updates based on player state
      if (playerState == PlayerState.playing) {
        _startPositionUpdates();
      } else {
        _stopPositionUpdates();
      }
    });
  }

  void _startPositionUpdates() {
    _positionTimer?.cancel();
    _positionTimer = Timer.periodic(const Duration(milliseconds: 100), (
      timer,
    ) async {
      try {
        final position = await _playerController.getDuration(
          DurationType.current,
        );
        add(UpdatePlayerPosition(Duration(milliseconds: position)));
      } catch (e) {
        // Handle position update error
      }
    });
  }

  void _stopPositionUpdates() {
    _positionTimer?.cancel();
  }

  Future<void> _onInitializeAudioPlayer(
    InitializeAudioPlayer event,
    Emitter<TranscriptionState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final groupedTranscriptions = _audioService.groupTranscriptionsByDuration(
        event.audio.transcript,
        15.0,
      );

      final localPath = await _audioService.prepareAudioFile(
        event.audio.storagePath,
      );

      emit(
        state.copyWith(
          localAudioPath: localPath,
          groupedTranscriptions: groupedTranscriptions,
          filteredTranscriptions: groupedTranscriptions,
          totalDuration: Duration(seconds: event.audio.duration),
          isLoading: false,
        ),
      );

      await _preparePlayer(localPath, emit);
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: e.toString()));
    }
  }

  Future<void> _preparePlayer(
    String localPath,
    Emitter<TranscriptionState> emit,
  ) async {
    try {
      // Use audio duration for dynamic sample calculation
      final audioDuration = state.totalDuration.inSeconds > 0
          ? state.totalDuration
          : null;

      final samples = _audioService.calculateOptimalSamples(
        400, // Better default width estimate for mobile screens
        audioDuration: audioDuration,
      );

      await _playerController.preparePlayer(
        path: localPath,
        shouldExtractWaveform: true,
        noOfSamples: samples,
        volume: 1.0,
      );

      emit(state.copyWith(isInitialized: true, isWaveformReady: true));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to prepare player: $e'));
    }
  }

  Future<void> _onPlayAudio(
    PlayAudio event,
    Emitter<TranscriptionState> emit,
  ) async {
    if (!state.isInitialized) return;

    try {
      if (state.isStopped) {
        await _preparePlayer(state.localAudioPath!, emit);
      }
      await _playerController.startPlayer();
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to play audio: $e'));
    }
  }

  Future<void> _onPauseAudio(
    PauseAudio event,
    Emitter<TranscriptionState> emit,
  ) async {
    if (!state.isInitialized) return;

    try {
      await _playerController.pausePlayer();
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to pause audio: $e'));
    }
  }

  Future<void> _onStopAudio(
    StopAudio event,
    Emitter<TranscriptionState> emit,
  ) async {
    if (!state.isInitialized) return;

    try {
      await _playerController.stopPlayer();
      emit(state.copyWith(currentPosition: Duration.zero));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to stop audio: $e'));
    }
  }

  Future<void> _onSeekAudio(
    SeekAudio event,
    Emitter<TranscriptionState> emit,
  ) async {
    if (!state.isInitialized) return;

    try {
      await _playerController.seekTo(event.position.inMilliseconds);
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to seek audio: $e'));
    }
  }

  void _onUpdatePlayerPosition(
    UpdatePlayerPosition event,
    Emitter<TranscriptionState> emit,
  ) {
    emit(state.copyWith(currentPosition: event.position));
  }

  void _onUpdatePlayerState(
    UpdatePlayerState event,
    Emitter<TranscriptionState> emit,
  ) {
    emit(state.copyWith(playerState: event.playerState));
  }

  void _onSearchTranscript(
    SearchTranscript event,
    Emitter<TranscriptionState> emit,
  ) {
    final filteredTranscriptions = _audioService.filterTranscriptions(
      state.groupedTranscriptions,
      event.query,
    );

    emit(
      state.copyWith(
        searchQuery: event.query,
        filteredTranscriptions: filteredTranscriptions,
      ),
    );
  }

  Future<void> _onJumpToTranscript(
    JumpToTranscript event,
    Emitter<TranscriptionState> emit,
  ) async {
    final position = Duration(
      milliseconds: (event.transcription.timestamp * 1000).round(),
    );

    add(SeekAudio(position));
  }

  void _onPlayerError(PlayerError event, Emitter<TranscriptionState> emit) {
    emit(state.copyWith(errorMessage: event.error));
  }

  void _onWaveformReady(WaveformReady event, Emitter<TranscriptionState> emit) {
    emit(state.copyWith(isWaveformReady: true));
  }

  Future<void> _onRecalculateWaveform(
    RecalculateWaveform event,
    Emitter<TranscriptionState> emit,
  ) async {
    if (!state.isInitialized || state.localAudioPath == null) return;

    try {
      // Calculate new optimal samples based on actual widget width
      final audioDuration = state.totalDuration.inSeconds > 0
          ? state.totalDuration
          : null;

      final newSamples = _audioService.calculateOptimalSamples(
        event.width,
        audioDuration: audioDuration,
      );

      // Only recalculate if the difference is significant (more than 20% change)
      final currentSamples = _audioService.calculateOptimalSamples(
        300, // Previous default width
        audioDuration: audioDuration,
      );

      if ((newSamples - currentSamples).abs() > (currentSamples * 0.2)) {
        // Significant change, recalculate waveform
        emit(state.copyWith(isWaveformReady: false));

        await _playerController.preparePlayer(
          path: state.localAudioPath!,
          shouldExtractWaveform: true,
          noOfSamples: newSamples,
          volume: 1.0,
        );

        emit(state.copyWith(isWaveformReady: true));
      }
    } catch (e) {
      // Silently handle errors to avoid disrupting playback
      print('Failed to recalculate waveform: $e');
    }
  }

  void _onDisposePlayer(DisposePlayer event, Emitter<TranscriptionState> emit) {
    _dispose();
  }

  void _dispose() {
    _playerStateSubscription?.cancel();
    _playerPositionSubscription?.cancel();
    _positionTimer?.cancel();
    _playerController.dispose();
  }

  @override
  Future<void> close() {
    _dispose();
    return super.close();
  }

  PlayerController get playerController => _playerController;
  AudioPlayerService get audioService => _audioService;
}
