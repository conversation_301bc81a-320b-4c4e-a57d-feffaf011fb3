import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/services/audio/audio_services.dart';

part 'audio_event.dart';
part 'audio_state.dart';

class AudioBloc extends Bloc<AudioEvent, AudioState> {
  final AudioServices _audioServices;

  AudioBloc({AudioServices? audioServices})
    : _audioServices = audioServices ?? AudioServices(),
      super(AudioInitial()) {
    on<GetAllAudiosEvent>((event, emit) async {
      emit(AudioLoading());

      try {
        final audios = await _audioServices.getAllAudios(event.userId);
        emit(AudioSuccess(audios));
      } catch (e) {
        emit(AudioError(e.toString()));
      }
    });

    on<GetAllAudiosStreamEvent>((event, emit) async {
      emit(AudioLoading());

      try {
        await emit.forEach<List<Audio>>(
          _audioServices.getAllAudiosStream(event.userId),
          onData: (audios) => AudioSuccess(audios),
          onError: (error, stackTrace) => AudioError(error.toString()),
        );
      } catch (e) {
        emit(AudioError(e.toString()));
      }
    });

    on<SearchAudioEvent>((event, emit) async {
      emit(AudioLoading());

      try {
        final audios = await _audioServices.searchAudios(
          event.searchQuery,
          event.userId,
        );
        emit(AudioSuccess(audios));
      } catch (e) {
        emit(AudioError(e.toString()));
      }
    });
  }

}
