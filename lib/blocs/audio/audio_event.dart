part of 'audio_bloc.dart';

@immutable
sealed class AudioEvent {}

class GetAllAudiosEvent extends AudioEvent {
  final String userId;
  GetAllAudiosEvent(this.userId);
}

class SearchAudioEvent extends AudioEvent {
  final String userId;
  final String searchQuery;

  SearchAudioEvent(this.userId, this.searchQuery);
}

class GetAllAudiosStreamEvent extends AudioEvent {
  final String userId;
  GetAllAudiosStreamEvent(this.userId);
}
