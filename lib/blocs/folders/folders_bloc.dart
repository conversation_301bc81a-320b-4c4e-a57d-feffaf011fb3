import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/services/folders/folders_services.dart';

part 'folders_event.dart';
part 'folders_state.dart';

class FoldersBloc extends Bloc<FoldersEvent, FoldersState> {
  final FoldersServices _foldersServices = FoldersServices();

  FoldersBloc() : super(FoldersInitial()) {
    on<CreateFolder>(_onCreateFolder);
    on<LoadFolders>(_onLoadFolders);
    on<UpdateFolder>(_onUpdateFolder);
  }

  Future<void> _onCreateFolder(
    CreateFolder event,
    Emitter<FoldersState> emit,
  ) async {
    try {
      final folder = await _foldersServices.createFolder(
        event.name,
        event.userId,
      );
      if (folder != null) {
        emit(FoldersCreated(folder));
      }
    } catch (e) {
      emit(FoldersError(e.toString()));
    }
  }

  Future<void> _onLoadFolders(
    LoadFolders event,
    Emitter<FoldersState> emit,
  ) async {
    try {
      emit(FoldersLoading());
      final folders = await _foldersServices.getUserFolders(event.userId);
      emit(FoldersLoaded(folders));
    } catch (e) {
      emit(FoldersLoaded([]));
    }
  }

  void _onUpdateFolder(UpdateFolder event, Emitter<FoldersState> emit) {
    emit(FolderUpdated(event.folder));
  }
}
