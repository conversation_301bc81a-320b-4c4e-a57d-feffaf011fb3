import 'dart:io';
import 'dart:typed_data';
import 'dart:async';
import 'package:record/record.dart' as record_pkg;
import 'package:waveform_flutter/waveform_flutter.dart' as waveform;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

class RecordingService {
  late record_pkg.AudioRecorder _audioRecorder;
  final StreamController<waveform.Amplitude> _amplitudeController = StreamController<waveform.Amplitude>.broadcast();
  StreamSubscription<record_pkg.Amplitude>? _amplitudeSubscription;

  record_pkg.AudioRecorder get recorder => _audioRecorder;
  Stream<waveform.Amplitude> get amplitudeStream => _amplitudeController.stream;

  String? _existingRecordingPath;
  String? _tempRecordingPath;

  Future<void> initialize() async {
    // Initialize record package for WAV recording
    _audioRecorder = record_pkg.AudioRecorder();

    // Check and request permission
    if (!await _audioRecorder.hasPermission()) {
      throw Exception('Microphone permission not granted');
    }
  }

  Future<String> startRecording() async {
    final directory = await getApplicationDocumentsDirectory();
    final wavPath = "${directory.path}/recordings_${DateTime.now().millisecondsSinceEpoch}.wav";

    // Start recording with record package (WAV format)
    await _audioRecorder.start(
      const record_pkg.RecordConfig(
        encoder: record_pkg.AudioEncoder.wav,
        sampleRate: 44100,
        numChannels: 1,
      ),
      path: wavPath,
    );

    // Start amplitude stream for waveform visualization
    _startAmplitudeStream();

    return wavPath;
  }

  Future<String?> stopRecording() async {
    print('🛑 RecordingService: Stopping recording...');

    // Stop record package recorder
    final wavPath = await _audioRecorder.stop();

    // Stop amplitude stream
    _stopAmplitudeStream();

    print('🎙️ WAV recording stopped. Path: $wavPath');

    String finalPath = wavPath ?? '';

    if (_existingRecordingPath != null && _tempRecordingPath != null) {
      try {
        final existingFile = File(_existingRecordingPath!);
        final tempFile = File(_tempRecordingPath!);

        if (await existingFile.exists() && await tempFile.exists()) {
          print('Merging WAV files: ${_existingRecordingPath!} + ${_tempRecordingPath!}');

          final mergedPath = await _mergeWavFiles(
            _existingRecordingPath!,
            _tempRecordingPath!,
          );

          if (await tempFile.exists()) {
            await tempFile.delete();
          }

          _existingRecordingPath = null;
          _tempRecordingPath = null;

          finalPath = mergedPath;
        } else {
          print('One of the files does not exist for merging');
          final fallbackPath = _existingRecordingPath ?? wavPath;
          _existingRecordingPath = null;
          _tempRecordingPath = null;
          finalPath = fallbackPath ?? '';
        }
      } catch (e) {
        print('❌ Merge error: $e');
        final fallbackPath = _existingRecordingPath ?? wavPath;

        _existingRecordingPath = null;
        _tempRecordingPath = null;

        finalPath = fallbackPath ?? '';
      }
    }

    if (finalPath.isEmpty) {
      return null;
    }

    return finalPath;
  }

  Future<String> _mergeWavFiles(
    String inputPath1,
    String inputPath2,
  ) async {
    final directory = await getApplicationDocumentsDirectory();
    final outputPath = "${directory.path}/merged_${DateTime.now().millisecondsSinceEpoch}.wav";

    try {
      print('🔄 Starting WAV merge: $inputPath1 + $inputPath2');

      final file1 = await File(inputPath1).readAsBytes();
      final file2 = await File(inputPath2).readAsBytes();

      print('📊 File 1 size: ${file1.length} bytes');
      print('📊 File 2 size: ${file2.length} bytes');

      if (file1.length < 44 || file2.length < 44) {
        throw Exception("One of the files is too short to be a valid WAV file.");
      }

      // Validate WAV headers
      if (!_isValidWavFile(file1) || !_isValidWavFile(file2)) {
        throw Exception("One or both files are not valid WAV files.");
      }

      // Parse WAV headers to find actual data start positions
      final dataStart1 = _findDataChunkStart(file1);
      final dataStart2 = _findDataChunkStart(file2);

      if (dataStart1 == -1 || dataStart2 == -1) {
        throw Exception("Could not find data chunk in one or both WAV files.");
      }

      print('📍 Data chunk starts: File1=$dataStart1, File2=$dataStart2');

      // Extract audio data (skip headers)
      final data1 = file1.sublist(dataStart1);
      final data2 = file2.sublist(dataStart2);

      print('📊 Audio data sizes: File1=${data1.length}, File2=${data2.length}');

      // Use the first file's header as template
      final baseHeader = file1.sublist(0, dataStart1);

      // Calculate new total data size
      final totalDataLength = data1.length + data2.length;

      // Create new WAV file with proper header
      final mergedWav = _createWavFile(baseHeader, totalDataLength, data1, data2);

      await File(outputPath).writeAsBytes(mergedWav);
      print('✅ Merged WAV saved to $outputPath (${mergedWav.length} bytes)');

      // Clean up input files
      await File(inputPath1).delete();
      await File(inputPath2).delete();
      print('🗑️ Cleaned up input files');

      return outputPath;
    } catch (e) {
      print('❌ WAV merge error: $e');
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }
      rethrow;
    }
  }

  bool _isValidWavFile(Uint8List bytes) {
    if (bytes.length < 44) return false;

    // Check RIFF header
    final riffHeader = String.fromCharCodes(bytes.sublist(0, 4));
    if (riffHeader != 'RIFF') return false;

    // Check WAVE format
    final waveFormat = String.fromCharCodes(bytes.sublist(8, 12));
    if (waveFormat != 'WAVE') return false;

    return true;
  }

  int _findDataChunkStart(Uint8List bytes) {
    // Look for "data" chunk identifier
    for (int i = 12; i < bytes.length - 4; i++) {
      if (bytes[i] == 0x64 && // 'd'
          bytes[i + 1] == 0x61 && // 'a'
          bytes[i + 2] == 0x74 && // 't'
          bytes[i + 3] == 0x61) { // 'a'
        // Found "data" chunk, return position after chunk header (8 bytes)
        return i + 8;
      }
    }
    return -1; // Data chunk not found
  }

  Uint8List _createWavFile(Uint8List baseHeader, int totalDataLength, Uint8List data1, Uint8List data2) {
    // Create a new WAV header based on the first file
    final newHeader = Uint8List(44);

    // Copy base header structure
    for (int i = 0; i < 44 && i < baseHeader.length; i++) {
      newHeader[i] = baseHeader[i];
    }

    final headerByteData = ByteData.sublistView(newHeader);

    // Update file size (ChunkSize = file size - 8)
    headerByteData.setUint32(4, totalDataLength + 36, Endian.little);

    // Update data chunk size (at offset 40 in standard WAV)
    headerByteData.setUint32(40, totalDataLength, Endian.little);

    // Combine header and data
    final result = Uint8List(44 + totalDataLength);
    result.setRange(0, 44, newHeader);
    result.setRange(44, 44 + data1.length, data1);
    result.setRange(44 + data1.length, 44 + totalDataLength, data2);

    return result;
  }





  Future<String> resumeRecording(String existingPath) async {
    final directory = await getApplicationDocumentsDirectory();
    final tempWavPath = "${directory.path}/temp_${DateTime.now().millisecondsSinceEpoch}.wav";

    final existingFile = File(existingPath);
    if (!await existingFile.exists()) {
      print('Warning: Existing recording file not found at $existingPath');

      final fileName = path.basename(existingPath);
      final reconstructedPath = path.join(directory.path, fileName);
      final reconstructedFile = File(reconstructedPath);

      if (await reconstructedFile.exists()) {
        print('Found recording at reconstructed path: $reconstructedPath');
        _existingRecordingPath = reconstructedPath;
        _tempRecordingPath = tempWavPath;

        // Start record package recorder for resume
        await _audioRecorder.start(
          const record_pkg.RecordConfig(
            encoder: record_pkg.AudioEncoder.wav,
            sampleRate: 44100,
            numChannels: 1,
          ),
          path: tempWavPath,
        );

        // Start amplitude stream
        _startAmplitudeStream();

        return reconstructedPath;
      } else {
        print('Recording file completely lost, starting fresh');
        return await startRecording();
      }
    }

    print('Resuming recording - existing: $existingPath, temp: $tempWavPath');

    _existingRecordingPath = existingPath;
    _tempRecordingPath = tempWavPath;

    // Start record package recorder for resume
    await _audioRecorder.start(
      const record_pkg.RecordConfig(
        encoder: record_pkg.AudioEncoder.wav,
        sampleRate: 44100,
        numChannels: 1,
      ),
      path: tempWavPath,
    );

    // Start amplitude stream
    _startAmplitudeStream();

    return existingPath;
  }

  Future<void> dispose() async {
    _stopAmplitudeStream();
    await _amplitudeController.close();
    await _audioRecorder.dispose();
  }

  Future<bool> get hasPermission => _audioRecorder.hasPermission();

  void _startAmplitudeStream() {
    // Listen to amplitude changes from the record package
    _amplitudeSubscription = _audioRecorder.onAmplitudeChanged(const Duration(milliseconds: 100))
        .listen((amplitude) {
      // Convert record package amplitude to waveform_flutter Amplitude
      final normalizedAmplitude = _normalizeAmplitude(amplitude.current);
      _amplitudeController.add(waveform.Amplitude(
        current: normalizedAmplitude,
        max: 100.0,
      ));
    });
  }

  void _stopAmplitudeStream() {
    _amplitudeSubscription?.cancel();
    _amplitudeSubscription = null;
  }

  double _normalizeAmplitude(double dbfs) {
    // Convert dBFS to a 0-100 scale for waveform visualization
    // dBFS ranges from -160 (silence) to 0 (max volume)
    // Higher dBFS values (closer to 0) = louder sound = higher waveform
    // Lower dBFS values (closer to -160) = quieter sound = lower waveform

    // Clamp the range for better visualization
    if (dbfs <= -80) return 5; // Very quiet threshold - show minimal wave
    if (dbfs >= -10) return 100; // Loud threshold - show max wave

    // Linear mapping from -80 to -10 dBFS -> 5 to 100
    // This ensures louder sounds create higher waves
    final normalized = ((dbfs + 80) / 70) * 95 + 5;
    return normalized.clamp(5, 100);
  }
}
