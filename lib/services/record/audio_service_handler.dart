import 'dart:async';
import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';

class RecordingAudioHandler extends BaseAudioHandler {
  static const String _recordingId = 'recording_session';
  Timer? _updateTimer;
  Function()? _onStopCallback;

  Duration _recordingDuration = Duration.zero;
  bool _isRecording = false;

  @override
  Future<void> prepare() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(
        AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
          avAudioSessionMode: AVAudioSessionMode.defaultMode,
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: false,
        ),
      );

      await super.prepare();
      print('AudioServiceHandler prepared successfully with audio session');
    } catch (e) {
      print('AudioServiceHandler prepare failed: $e');
    }
  }

  // Override all unwanted media actions to prevent them from appearing
  @override
  Future<void> play() async {
    // Do nothing - we don't want play functionality for recording
  }

  @override
  Future<void> seek(Duration position) async {
    // Do nothing - no seek functionality for recording
  }

  @override
  Future<void> skipToNext() async {
    // Do nothing - no skip functionality for recording
  }

  @override
  Future<void> skipToPrevious() async {
    // Do nothing - no skip functionality for recording
  }

  @override
  Future<void> fastForward() async {
    // Do nothing - no fast forward for recording
  }

  @override
  Future<void> rewind() async {
    // Do nothing - no rewind for recording
  }

  Future<void> startRecording({required Function() onStopPressed}) async {
    await prepare();

    try {
      final session = await AudioSession.instance;
      await session.setActive(true);
      print('AudioServiceHandler: Audio session activated');
    } catch (e) {
      print('AudioServiceHandler: Failed to activate audio session: $e');
    }

    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    final formattedDuration = formatDuration(_recordingDuration);
    final mediaItem = MediaItem(
      id: _recordingId,
      title: Platform.isAndroid ? '🔴 Recording Audio' : 'Recording Audio',
      artist: Platform.isAndroid ? 'Duration: $formattedDuration' : formattedDuration,
      album: 'Sluqe',
      duration: null, // No duration for live recording
      artUri: null,   // No artwork
      extras: {
        'isRecording': true,
        'recordingDuration': _recordingDuration.inSeconds,
      },
    );

    this.mediaItem.add(mediaItem);
    print('AudioServiceHandler: Media item set - ${mediaItem.title}');

    // Create platform-specific playback state with ONLY stop control
    final playbackStateObj = PlaybackState(
      controls: [
        MediaControl.stop, // Only stop button
      ],
      systemActions: {
        MediaAction.stop, // Only stop action
      },
      // Platform-specific configuration
      playing: Platform.isAndroid ? true : false, // Android needs true, iOS can be false
      processingState: AudioProcessingState.ready,
      updatePosition: Platform.isAndroid ? _recordingDuration : Duration.zero,
      bufferedPosition: Platform.isAndroid ? _recordingDuration : Duration.zero,
      speed: Platform.isAndroid ? 1.0 : 0.0, // Android needs normal speed, iOS doesn't
    );

    playbackState.add(playbackStateObj);
    print(
      'AudioServiceHandler: Playback state set - recording mode',
    );

    _startDurationUpdates();

    print('AudioServiceHandler: Recording session started successfully');
  }

  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    final currentItem = mediaItem.value;
    if (currentItem != null) {
      final formattedDuration = formatDuration(duration);
      final updatedItem = currentItem.copyWith(
        title: Platform.isAndroid ? '🔴 Recording Audio' : 'Recording Audio',
        artist: Platform.isAndroid ? 'Duration: $formattedDuration' : formattedDuration,
        extras: {
          ...currentItem.extras ?? {},
          'recordingDuration': duration.inSeconds,
        },
      );
      mediaItem.add(updatedItem);
    }

    // Update platform-specific playback state with only stop control
    playbackState.add(
      PlaybackState(
        controls: [
          MediaControl.stop, // Only stop button
        ],
        systemActions: {
          MediaAction.stop, // Only stop action
        },
        // Platform-specific configuration
        playing: Platform.isAndroid ? true : false, // Android needs true, iOS can be false
        processingState: AudioProcessingState.ready,
        updatePosition: Platform.isAndroid ? duration : Duration.zero,
        bufferedPosition: Platform.isAndroid ? duration : Duration.zero,
        speed: Platform.isAndroid ? 1.0 : 0.0, // Android needs normal speed, iOS doesn't
      ),
    );
  }

  Future<void> stopRecording() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    try {
      final session = await AudioSession.instance;
      await session.setActive(false);
      print('AudioServiceHandler: Audio session deactivated');
    } catch (e) {
      print('AudioServiceHandler: Failed to deactivate audio session: $e');
    }

    playbackState.add(
      PlaybackState(
        controls: [],
        systemActions: {},
        playing: false,
        processingState: AudioProcessingState.idle,
        updatePosition: Duration.zero,
      ),
    );

    mediaItem.add(null);
  }

  @override
  Future<void> stop() async {
    await stopRecording();
    _onStopCallback?.call();
    await super.stop();
  }

  @override
  Future<void> pause() async {
    await stop();
  }

  void _startDurationUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
    });
  }

  @override
  Future<void> onTaskRemoved() async {
    await stop();
    await super.onTaskRemoved();
  }

  @override
  Future<void> onNotificationDeleted() async {
    await stop();
    await super.onNotificationDeleted();
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  bool get isRecording => _isRecording;

  Duration get recordingDuration => _recordingDuration;
}
