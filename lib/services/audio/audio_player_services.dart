import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:sluqe/models/audio.dart';

class AudioPlayerService {
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  factory AudioPlayerService() => _instance;
  AudioPlayerService._internal();

  Future<String> downloadAndCacheAudio(String url) async {
    final directory = await getTemporaryDirectory();

    final bytes = utf8.encode(url);
    final digest = sha1.convert(bytes);
    final fileExtension = url.contains('.wav') ? 'wav' : (url.contains('.mp3') ? 'mp3' : 'm4a');

    final fileName = '$digest.$fileExtension';
    final filePath = '${directory.path}/$fileName';
    final file = File(filePath);

    if (await file.exists()) {
      print("File found in cache: $filePath");
      return filePath;
    } else {
      print("File not in cache. Downloading from: $url");
      try {
        final response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          await file.writeAsBytes(response.bodyBytes);
          print("File downloaded and saved to: $filePath");
          return filePath;
        } else {
          throw Exception(
            'Failed to download file. Status code: ${response.statusCode}',
          );
        }
      } catch (e) {
        throw Exception('Error downloading file: $e');
      }
    }
  }

  Future<String> getFirebaseDownloadUrl(String storagePath) async {
    try {
      final ref = FirebaseStorage.instance.ref(storagePath);
      print("Getting download URL for: $storagePath");
      final String downloadUrl = await ref.getDownloadURL();
      print("Download URL obtained: $downloadUrl");
      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to get download URL: $e');
    }
  }

  Future<String> prepareAudioFile(String storagePath) async {
    final downloadUrl = await getFirebaseDownloadUrl(storagePath);
    final localPath = await downloadAndCacheAudio(downloadUrl);
    return localPath;
  }

  List<Transcription> groupTranscriptionsByDuration(
    List<Transcription> transcriptions,
    double durationInSeconds,
  ) {
    if (transcriptions.isEmpty) return [];

    List<Transcription> grouped = [];
    double currentSectionStart = transcriptions.first.timestamp;
    String currentText = '';

    for (int i = 0; i < transcriptions.length; i++) {
      final transcript = transcriptions[i];
      final cleanText = transcript.text.trim();

      if (transcript.timestamp - currentSectionStart >= durationInSeconds) {
        if (currentText.isNotEmpty) {
          grouped.add(
            Transcription(
              timestamp: currentSectionStart,
              text: _normalizeText(currentText),
            ),
          );
        }
        currentSectionStart = transcript.timestamp;
        currentText = cleanText;
      } else {
        if (currentText.isEmpty) {
          currentText = cleanText;
        } else {
          currentText += ' $cleanText';
        }
      }
    }

    if (currentText.isNotEmpty) {
      grouped.add(
        Transcription(
          timestamp: currentSectionStart,
          text: _normalizeText(currentText)
        ),
      );
    }

    return grouped;
  }

  String _normalizeText(String text) {
    return text
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'^\s+|\s+$'), '');
  }

  List<Transcription> filterTranscriptions(
    List<Transcription> transcriptions,
    String query,
  ) {
    if (query.trim().isEmpty) {
      return transcriptions;
    }

    final cleanQuery = query.trim().toLowerCase();
    return transcriptions.where((transcript) {
      final cleanText = transcript.text.trim().toLowerCase();
      return cleanText.contains(cleanQuery);
    }).toList();
  }

  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return "$minutes:$seconds";
  }

  Future<String> getAssetFile(String assetPath) async {
    final byteData = await rootBundle.load(assetPath);
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/${assetPath.split('/').last}');
    await file.writeAsBytes(byteData.buffer.asUint8List());
    return file.path;
  }

  int calculateOptimalSamples(double width, {Duration? audioDuration}) {
    if (audioDuration == null) {
      // Fallback to original calculation if no duration provided
      return (width / 2).round();
    }

    // Calculate samples based on audio duration for better waveform representation
    final durationInSeconds = audioDuration.inSeconds;

    if (durationInSeconds <= 0) {
      return (width / 2).round();
    }

    // Dynamic sample calculation:
    // - Short audio (< 30s): More samples per second for detail
    // - Medium audio (30s-5min): Balanced samples
    // - Long audio (> 5min): Fewer samples to avoid performance issues

    int samplesPerSecond;
    if (durationInSeconds < 30) {
      samplesPerSecond = 10; // High detail for short recordings
    } else if (durationInSeconds < 300) { // 5 minutes
      samplesPerSecond = 5; // Medium detail
    } else {
      samplesPerSecond = 2; // Lower detail for long recordings
    }

    final calculatedSamples = durationInSeconds * samplesPerSecond;

    // Ensure we don't exceed reasonable limits
    final maxSamples = (width * 2).round(); // Max 2 samples per pixel
    final minSamples = (width / 4).round(); // Min 1 sample per 4 pixels

    return calculatedSamples.clamp(minSamples, maxSamples);
  }
}
