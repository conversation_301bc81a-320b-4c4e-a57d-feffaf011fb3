import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sluqe/models/audio.dart';
import 'package:sluqe/models/folder.dart';
import 'package:sluqe/services/folders/folders_services.dart';

class AudioServices {
  final Map<String, String> _folderCache = {};
  final _firestore = FirebaseFirestore.instance;
  final _folderServices = FoldersServices();

  Future<List<Audio>> getAllAudios(String userId) async {
    try {
      final snapshot =
          await _firestore
              .collection('users')
              .doc(userId)
              .collection('audios')
              .orderBy('createdAt', descending: true)
              .get();

      List<Audio> audios = [];

      for (QueryDocumentSnapshot doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

        if (data['deletedAt'] != null) {
          continue;
        }

        String folderId = data['folderId'] ?? '';
        if (folderId.isNotEmpty && !_folderCache.containsKey(folderId)) {
          Folder folder = await _folderServices.getFolderById(folderId, userId);
          _folderCache[folderId] = folder.path;
        }

        data['folderName'] = _folderCache[folderId] ?? '';

        Audio audio = Audio.fromMap(data);
        audios.add(audio);
      }

      return audios;
    } catch (e) {
      print('Error getting audios: $e');
      throw Exception('Failed to get audios: $e');
    }
  }

  Stream<List<Audio>> getAllAudiosStream(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('audios')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .asyncMap((snapshot) async {
      List<Audio> audios = [];

      for (QueryDocumentSnapshot doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

        if (data['deletedAt'] != null) {
          continue;
        }

        String folderId = data['folderId'] ?? '';
        if (folderId.isNotEmpty && !_folderCache.containsKey(folderId)) {
          Folder folder = await _folderServices.getFolderById(folderId, userId);
          _folderCache[folderId] = folder.path;
        }

        data['folderName'] = _folderCache[folderId] ?? '';

        Audio audio = Audio.fromMap(data);
        audios.add(audio);
      }

      return audios;
    });
  }

  Future<List<Audio>> searchAudios(String searchQuery, String userId) async {
    final audios = await getAllAudios(userId);
    if (searchQuery.isEmpty) return audios;

    final filteredAudios = audios
        .where(
          (audio) =>
              audio.title.toLowerCase().contains(searchQuery.toLowerCase()),
        )
        .toList();

    // Maintain the same sorting order (newest first) for search results
    filteredAudios.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return filteredAudios;
  }
}
